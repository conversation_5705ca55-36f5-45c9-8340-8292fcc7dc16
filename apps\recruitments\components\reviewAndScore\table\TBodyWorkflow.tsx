import { memo } from "react";
import { useNavigate } from "react-router-dom";
import { useTypedSelector } from "../../../store";
import play from "../../../image/icon/Vector.png";


const reviewAndScoreFun = state => state.review_and_score
const TBodyReviewAndScore = ({
	                       horizontalScrollState
                       }: { horizontalScrollState: boolean}) => {
	const table = useTypedSelector(reviewAndScoreFun)

	const navigate = useNavigate()
	return <tbody className="table__tbody">
	{
		table.tableItems?.map(item => {
			return <tr className="table__tr" key={item.id}>
				<td className={`table__td sticky ${horizontalScrollState ? 'moved' : ''} manage-team__column__middle`}>
					<p className='manage-team__text manage-team__text--link' style={{fontWeight: 900}} onClick={()=>navigate(`/recruitment/candidate-profile/${item.userId}`)}>{item.candidateName}</p>
				</td>
				{
					table.tabFilter.map(value => {
						if (value.id === 2) {
							return value.active &&
                  <td className="table__td actions manage-team__column__middle" key={value.displayName}>
                      <p className='manage-team__text manage-team__text--gray7'>
					 { item.jobTitle}
                      </p>
                  </td>
						}
						if (value.id === 3) {
							return value.active &&
                  <td className="table__td actions manage-team__column__middle" key={value.displayName}>
                      <p className='manage-team__text manage-team__text--green'>{item.assessmentType === "live-coding" ? "Live Coding" : "Take Home Task"}</p>
                  </td>
						}

						if (value.id === 4) {
							return value.active &&
				  <td className="table__td actions manage-team__column__middle" key={value.displayName}>
					  <p className='manage-team__text manage-team__text--black' style={{textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden"}}>{item?.questionName.length > 35  ? item.questionName.slice(0,30) + '...' : item.questionName}</p>
				  </td>
						}

						if (value.id === 5) {
							return value.active &&
                  <td className="table__td actions manage-team__column__middle" key={value.displayName}>
									{item.assessmentType === 'take-home-task' ? (
										<div className="review-action" onClick={() => {
											navigate(`/recruitment/take-home-submission/${item.submissionId}`, { state: item })
										}} style={{display: 'flex', alignItems: 'center', gap: '8px', cursor: 'pointer'}}>
											<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
												<path d="M1 4L8 1L15 4V12C15 12.5304 14.7893 13.0391 14.4142 13.4142C14.0391 13.7893 13.5304 14 13 14H3C2.46957 14 1.96086 13.7893 1.58579 13.4142C1.21071 13.0391 1 12.5304 1 12V4Z" stroke="#374151" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
												<path d="M8 7V11" stroke="#374151" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
											</svg>
											<p className='manage-team__text manage-team__text--black' style={{fontWeight: 900, margin: 0}}>Review</p>
										</div>
									) : (
										<div className="playback-action" onClick={() => {
											navigate(`/live-coding/playback/${item.userId}/${item.jobId}/${item.assessmentId}/${item.questionId}/${item?.playbackId}`, { state: item })
										}} style={{display: 'flex', alignItems: 'center', gap: '8px', cursor: 'pointer'}}>
											<img src={play} alt="playback-icon" className="manageAssignment__table__icon 'manage-team__text manage-team__text--black"/>
											<p className='manage-team__text manage-team__text--black' style={{fontWeight: 900, margin: 0}}>Playback</p>
										</div>
									)}
                  </td>
						}
						if (value.id === 6) {
							return value.active &&
				  <td className="table__td actions manage-team__column__middle" key={value.displayName}>
					  <p className='manage-team__text manage-team__text--black' >{item.questionId}</p>
				  </td>
						}
					})
				}
			</tr>
		})
	}
	</tbody>
}

export default memo(TBodyReviewAndScore)