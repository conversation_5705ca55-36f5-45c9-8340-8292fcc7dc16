
// Take-Home Submission Interfaces
export interface ITakeHomeSubmission {
	id: string;
	takeHomeTaskId: string;
	takeHomeAssignmentId: string;
	candidateId: string;
	candidateName?: string;
	jobId: string;
	jobTitle?: string;
	submittedAt: string;
	duration: number;
	score?: number;
	feedback?: string;
	language: string;
	questionSubmissions: IQuestionSubmission[];
	testCaseSummary: ITestCaseSummary;
	reviewData?: ISubmissionReviewData;
}

export interface IQuestionSubmission {
	questionId: string;
	questionName: string;
	submittedCode: string;
	testCaseResults: ITestCaseResult[];
	executionSummary: IExecutionSummary;
}

export interface ITestCaseResult {
	testCaseId: string;
	passed: boolean;
	input: string;
	expectedOutput: string;
	actualOutput: string;
	executionTime?: number;
	errorMessage?: string;
}

export interface IExecutionSummary {
	totalTestCases: number;
	passedTestCases: number;
	failedTestCases: number;
	successRate: number;
	totalExecutionTime: number;
}

export interface ITestCaseSummary {
	totalQuestions: number;
	totalTestCases: number;
	totalPassedTestCases: number;
	totalFailedTestCases: number;
	overallSuccessRate: number;
	questionSummaries: IQuestionTestSummary[];
}

export interface IQuestionTestSummary {
	questionId: string;
	questionName: string;
	totalTestCases: number;
	passedTestCases: number;
	failedTestCases: number;
	successRate: number;
}

export interface ISubmissionReviewData {
	reviewerId?: string;
	reviewerName?: string;
	reviewedAt?: string;
	feedback?: string;
	score?: number;
	codeQuality?: number;
	problemSolving?: number;
	testCoverage?: number;
	notes?: string;
}

export interface IReviewAndScore{
	assignPopup:boolean
	isLoading:boolean,
	popupWithList:{
		title:string,
		open:boolean,
		id:number,
		workflows:Array<any>
	}
	emptySearch: boolean,
	emptyTable: boolean,
	filters: {searchValue: string, sortBy: string, sortType: string,assessmentType: string},
	tableItems: [],
	fixedTab: { id: number, displayName: string, },
	tabFilter: Array<{ id: number, displayName: string, active: boolean, dbName: string }>
	pagination: {
		currentPage: number,
		limit: number,
		totalCount: number
	},
	deletePopup:{
		open:boolean,
		id:number,
		title:string
	}
}

